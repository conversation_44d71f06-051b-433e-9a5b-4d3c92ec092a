#!/usr/bin/env python3
"""
Test script to verify Redis connection and basic functionality
"""

import os
import sys
import json
import time

def test_redis_connection():
    """Test Redis connection and basic operations"""
    try:
        import redis
        
        # Get Redis configuration from environment variables
        redis_host = os.getenv('REDIS_HOST', 'redis')
        redis_port = int(os.getenv('REDIS_PORT', '6379'))
        redis_db = int(os.getenv('REDIS_DB', '0'))
        
        print(f"Connecting to Redis at {redis_host}:{redis_port}, DB: {redis_db}")
        
        # Initialize Redis client
        redis_client = redis.Redis(
            host=redis_host, 
            port=redis_port, 
            db=redis_db, 
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True
        )
        
        # Test connection
        print("Testing Redis connection...")
        redis_client.ping()
        print("✅ Redis connection successful!")
        
        # Test basic operations
        print("\nTesting basic Redis operations...")
        
        # Test string operations
        test_key = "test:connection"
        test_value = "Hello Redis!"
        redis_client.set(test_key, test_value, ex=60)  # Expire in 60 seconds
        retrieved_value = redis_client.get(test_key)
        assert retrieved_value == test_value, f"Expected {test_value}, got {retrieved_value}"
        print("✅ String operations working")
        
        # Test JSON operations (for cache data)
        test_json_key = "test:json"
        test_json_data = {"user_id": 123, "restrictions": ["blocked_user_1", "blocked_user_2"]}
        redis_client.setex(test_json_key, 300, json.dumps(test_json_data))  # 5 minutes
        retrieved_json = json.loads(redis_client.get(test_json_key))
        assert retrieved_json == test_json_data, f"JSON data mismatch"
        print("✅ JSON operations working")
        
        # Test expiration
        redis_client.setex("test:expire", 1, "expires_soon")
        time.sleep(2)
        expired_value = redis_client.get("test:expire")
        assert expired_value is None, "Key should have expired"
        print("✅ Expiration working")
        
        # Get Redis info
        info = redis_client.info()
        print(f"\n📊 Redis Info:")
        print(f"   Version: {info.get('redis_version', 'Unknown')}")
        print(f"   Memory Used: {info.get('used_memory_human', 'Unknown')}")
        print(f"   Connected Clients: {info.get('connected_clients', 'Unknown')}")
        print(f"   Total Commands: {info.get('total_commands_processed', 'Unknown')}")
        
        # Clean up test keys
        redis_client.delete(test_key, test_json_key)
        print("\n🧹 Test keys cleaned up")
        
        print("\n🎉 All Redis tests passed successfully!")
        return True
        
    except ImportError:
        print("❌ Redis module not installed. Run: pip install redis")
        return False
    except redis.ConnectionError as e:
        print(f"❌ Redis connection failed: {e}")
        print("Make sure Redis server is running and accessible")
        return False
    except Exception as e:
        print(f"❌ Redis test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Redis Connection Test")
    print("=" * 50)
    
    success = test_redis_connection()
    
    if success:
        print("\n✅ Redis is ready for use with SoTrue Backend!")
        sys.exit(0)
    else:
        print("\n❌ Redis setup needs attention")
        sys.exit(1)
