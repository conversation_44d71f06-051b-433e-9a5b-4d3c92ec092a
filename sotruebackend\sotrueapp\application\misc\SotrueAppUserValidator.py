from components.AppValidatorBase import AppValidatorBase as v

class SotrueAppUserValidator(v):

    validation_rules = {

        "APP_LOGIN":{
                "user_id":(v.required,(v.maxlength,256)),
                "password":(v.required,(v.maxlength,4096)),                
            },
        "GMAIL_LOGIN":{
                "user_id":(v.required,(v.maxlength,256)),
                "password":(v.required,(v.maxlength,4096)),                
            },
        "APPLE_LOGIN":{  
                "user_id":(v.optional,(v.maxlength,256)),
                "password":(v.optional,(v.maxlength,4096)), 
                "key":(v.optional,(v.maxlength,4096)), 
            },
        "GET_USER_SERVER":{
                "user_id":(v.optional,(v.email,),(v.maxlength,64)),              
            },
        "APP_REGISTER":{
                "full_name":(v.optional,(v.alphanumericspecial,),(v.maxlength,64)),
                "password":(v.required,(v.mincaps,),(v.minnumeric,),(v.minspecial,),(v.notallowed,),(v.minlengthpwd,8)),
                "email":(v.optional,(v.email,),(v.maxlength,64)),
                "country":(v.optional,(v.alphanumericspecial,),(v.maxlength,64)),
                "state":(v.optional,(v.alphanumericspecial,),(v.maxlength,64)),
                "referral_code":(v.optional,(v.alphanumericspecial,),(v.maxlength,8)),
                "mode":(v.optional,(v.values,{'REGULAR','GMAIL','APPLE'})),
                "type":(v.optional,(v.values,{'DUMMY','NORMAL'})),
            },
        "SUBMIT_OTP":{
                "user_seq":(v.required,(v.numeric,)),
                "otp":(v.required,(v.numeric,),(v.length,4)),
            },
        "SUBMIT_OTP_NEW":{
                "mobile":(v.required,(v.numeric,),(v.maxlength,12)),
                "email":(v.optional,(v.email,),(v.maxlength,64)),
                "otp":(v.required,(v.numeric,),(v.length,4)),
            },
        "SUBMIT_OTP_EMAIL":{
                "mobile":(v.required,(v.numeric,),(v.maxlength,12)),
                "email":(v.required,(v.email,),(v.maxlength,64)),
                "otp":(v.required,(v.numeric,),(v.length,4)),
            },
        "GENERATE_OTP":{
                "mobile":(v.required,(v.numeric,),(v.maxlength,12)),                 
            },
        "GENERATE_OTP_EMAIL":{
                "email":(v.required,(v.email,),(v.maxlength,64)),
                "mobile":(v.required,(v.numeric,),(v.maxlength,12)),
            },
        "VERIFY_USER_NAME": {
                "user_name":(v.required,(v.alphanumericspecial,),(v.maxlength,64)),
                "mobile":(v.required,(v.numeric,),(v.maxlength,12)),
            },
        "REGISTER_USER": {
                "full_name":(v.required,(v.alphanumericspecial,),(v.maxlength,64)),
                "user_name":(v.required,(v.alphanumericspecial,),(v.maxlength,64)),
                "mobile":(v.required,(v.numeric,),(v.maxlength,12)),
                "email":(v.required,(v.email,),(v.maxlength,64)),
            },
        "RESEND_OTP": {                
                "mobile":(v.required,(v.numeric,),(v.maxlength,12)),
                "email":(v.optional,(v.email,),(v.maxlength,64)),
                "type":(v.required,(v.values,{'MOBILE','EMAIL'})),
            },
        "SEND_PASSWORD_OTP":{
                "email":(v.required,(v.email,),(v.maxlength,64)),
            },
        "SUBMIT_PASSWORD_OTP":{
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "user_seq":(v.required,(v.numeric,)),
                "otp":(v.required,(v.numeric,),(v.length,4)),
            },
        "SET_NEW_PASSWORD":{
                "user_seq":(v.required,(v.numeric,)),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "otp":(v.required,(v.numeric,),(v.length,4)),
                "password":(v.required,(v.mincaps,),(v.minnumeric,),(v.minspecial,),(v.notallowed,),(v.minlengthpwd,8)),
            },
        "CHANGE_PASSWORD":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "user_seq":(v.required,(v.numeric,)),
                "old_password":(v.required,),
                "new_password":(v.required,(v.mincaps,),(v.minnumeric,),(v.minspecial,),(v.notallowed,),(v.minlengthpwd,8)),
            },
        "GET_CODE_VALUES":{                
                "code_type": (v.required,(v.values,{'CATEGORY','POST_CATEGORY','HELP_OPTION','RESOLUTION_CODE','DASHBOARD_PERIOD','RESTRICT_CODES','BLOCK_CODES','STATE_INDIA','DELETE_OPTION','USER_REPORT_PERIOD','DOCU_TYPE'})),
            },
        "SUBMIT_POST":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "file_format": (v.required,(v.values,{'JPG','JPEG','PNG','MP4','MPEG','QUICKTIME','HEIC'})),
                "comments":(v.optional,(v.notallowedexq,),(v.maxlength,2048)),
                "post_type":(v.required,(v.values,{'PAID','FREE'})),
                "viewer_fee":(v.optional,(v.float,)),
                "expire_on":(v.optional,(v.date,'%Y-%m-%d')),
                "media_type":(v.required,(v.values,{'IMAGE','VIDEO'})),
                "tagged_users":(v.optional,),
                "topics":(v.optional,),
                "reactions":(v.optional,),
                "is_playlist":(v.optional,(v.values,{'YES','NO'})),
                "location":(v.optional,(v.alphanumericspecial,),(v.maxlength,256)),
                "schedule":(v.optional,(v.datetime,'%Y-%m-%d %H:%M:%S')),
            },
        "DELETE_POST":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "post_seq":(v.required,(v.numeric,)),                
            },
        "GET_POSTS_USER":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "req_profile_seq":(v.required,(v.numeric,)),
                "media_type": (v.required,(v.values,{'ALL','IMAGE','VIDEO'})),
                "first_seq":(v.optional,(v.numeric,)),
            },
        "GET_USER_POST":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "post_seq":(v.required,(v.numeric,)),
            },
        "SUBMIT_POST_LIKE":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "post_seq":(v.required,(v.numeric,)),   
                "type": (v.optional,(v.values,{'LIKE','MEH','LIT',"ANGER","AUD","BOO","BLADY","BMAN","CELEB","CLAP","DISG","FEAR","SURP","TEASE","TPASS","WEVER","YAY","YUM"})),
            },
        "REMOVE_POST_LIKE":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "post_seq":(v.required,(v.numeric,)),   
                "type": (v.optional,(v.values,{'LIKE','MEH','LIT',"ANGER","AUD","BOO","BLADY","BMAN","CELEB","CLAP","DISG","FEAR","SURP","TEASE","TPASS","WEVER","YAY","YUM"})),
            },
        "SUBMIT_POST_VIEW":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "post_seq":(v.required,(v.numeric,)),                
            },        
        "SUBMIT_POST_COMMENT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "post_seq":(v.required,(v.numeric,)),
                "comment":(v.required,(v.notallowedexq,),(v.maxlength,2048)),
            },
        "REMOVE_POST_COMMENT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "post_seq":(v.required,(v.numeric,)),
                "comment_seq":(v.required,(v.numeric,)),
            },
        "SUBMIT_COMMENT_LIKE":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "post_seq":(v.required,(v.numeric,)),
                "comment_seq":(v.required,(v.numeric,)),
            },
        "REMOVE_COMMENT_LIKE":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "comment_seq":(v.required,(v.numeric,)),                
            },
        "GET_POST_COMMENTS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "post_seq":(v.required,(v.numeric,)),
                "profile_seq":(v.required,(v.numeric,)),
            },
        "SUBMIT_COMMENT_COMMENT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "comment_seq":(v.required,(v.numeric,)),
                "comment":(v.required,(v.alphanumericspecial,),(v.maxlength,2048)),
            },
        "REMOVE_COMMENT_COMMENT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),                
                "comment_seq":(v.required,(v.numeric,)),
            },
        "SAVE_USER_PROFILE":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "user_name":(v.required,(v.alphanumericspecial,),(v.maxlength,64)),
                "display_name":(v.required,(v.alphanumericspecial,),(v.maxlength,64)),
                "user_bio":(v.optional,(v.notallowedexq,),(v.maxlength,1024)),
                "category":(v.required,(v.alphanumericspecial,)),
                "disp_category":(v.required,(v.values,{'YES','NO'})),
                "fb_link":(v.optional,(v.url,),(v.maxlength,256)),
                "twiter_link":(v.optional,(v.url,),(v.maxlength,256)),
                "insta_link":(v.optional,(v.url,),(v.maxlength,256)),
                "country":(v.optional,(v.alphanumericspecial,),(v.maxlength,64)),
                "state":(v.optional,(v.alphanumericspecial,),(v.maxlength,64)),
                "reset_profile":(v.optional,(v.values,{'YES'})),
                "reset_cover":(v.optional,(v.values,{'YES'})),
                "gender":(v.optional,(v.values,{'MALE','FEMALE'})),
            },
        "GET_USER_PROFILE":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "req_profile_seq":(v.required,(v.numeric,)),
                "user_handle":(v.optional,(v.alphanumeric,),(v.maxlength,64))
            },
        "SAVE_ACCOUNT_SETTING":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "user_seq":(v.required,(v.numeric,)),
                "email":(v.optional,(v.email,),(v.maxlength,64)),
                "mobile":(v.optional,(v.numeric,),(v.maxlength,12)),
                "profile_type":(v.required,(v.values,{'PAID','NOTPAID'})),
                "bank_account":(v.optional,(v.numeric,"X"),(v.maxlength,32)),
                "ifsc_code":(v.optional,(v.ifsc,)),
                "account_holder":(v.optional,(v.alphaspace,),(v.maxlength,64)),
                "account_type":(v.optional,(v.values,{'SAVINGS','CURRENT'})),
                "viewer_fees":(v.optional,(v.numeric,)),
                "pan":(v.optional,(v.alphanumeric,),(v.maxlength,12)),
                "gstn":(v.optional,(v.length,15),(v.allcaps,),(v.gstn,)),
                "country":(v.optional,(v.alphanumericspecial,),(v.maxlength,64)),
                "state":(v.optional,(v.alphanumericspecial,),(v.maxlength,64)),
            },
        "GET_ACCOUNT_SETTING":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "user_seq":(v.required,(v.numeric,)),
            },
        "GET_POSTS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "user_seq":(v.required,(v.numeric,)),
                "media_type": (v.optional,(v.values,{'ALL','IMAGE','VIDEO'})),
                "first_seq":(v.optional,(v.numeric,)),
            },
        "FOLLOW_PROFILE":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "follow_profile_seq":(v.required,(v.numeric,)),
            },
        "UNFOLLOW_PROFILE":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "un_profile_seq":(v.required,(v.numeric,)),
            },
        "GET_FOLLOWERS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
            },
        "GET_FOLLOWER_COUNT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)), 
                "req_profile_seq":(v.required,(v.numeric,)),
            },
        "GET_FOLLOWING":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
            },
        "GET_FOLLOWING_COUNT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
            },
        "CHECK_USER_FOLLOWING":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "follow_profile_seq":(v.required,(v.numeric,)),
            },
        "ADD_BOOKMARK":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "post_seq":(v.required,(v.numeric,)),
            },
        "REMOVE_BOOKMARK":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "post_seq":(v.required,(v.numeric,)),
            },
        "GET_BOOKMARKS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
            },
        "GET_BOOKMARK_COUNT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
            },
        "GET_PROFILE_SUGGESTIONS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
            },
        "SUBMIT_STORY":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "file_format": (v.required,(v.values,{'JPG','JPEG','PNG','MP4','MPEG','QUICKTIME','HEIC'})),
                "caption":(v.optional,(v.notallowedexq,),(v.maxlength,2048)),                            
                "media_type":(v.required,(v.values,{'IMAGE','VIDEO'})),
            },
        "GET_STORIES":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
            },
        "GET_PROFILE_STORIES":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
            },
        "DELETE_STORY":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "story_seq":(v.required,(v.numeric,)),
                "profile_seq":(v.required,(v.numeric,)),
            },
        "GET_MEDIA_DATA":{
                "media_key":(v.required,(v.alphanumeric,)),                
            },
        "GET_MEDIA_URL_POST":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "post_seq":(v.required,(v.numeric,)),
            },
        "GET_MEDIA_URL_POFILE":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
            },
        "GET_PRIVACY_SETTINGS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
            },
        "UPDATE_PRIVACY_SETTINGS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "show_fan_count": (v.required,(v.values,{'YES','NO'})),
                "show_media_count": (v.required,(v.values,{'YES','NO'})),
                "enable_comment": (v.required,(v.values,{'YES','NO'})),
                "enable_watermark": (v.required,(v.values,{'YES','NO'})),
            },
        "GET_MEDIA_COUNT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
            },
        "DO_SEARCH":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "search_str":(v.optional,(v.alphanumericspecial,)),                
            },
        "DO_PLAYLIST_SEARCH":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "search_str":(v.optional,(v.alphanumericspecial,)),                
            },
        "DO_TOPIC_SEARCH":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "search_str":(v.optional,(v.alphanumericspecial,)),                
            },
        "TYPE_AHEAD_SEARCH":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "search_str":(v.required,(v.alphanumeric,)),                
            },
        "GET_PAYMENT_ORDER":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "user_seq":(v.required,(v.numeric,)),
                "amount":(v.required,(v.numeric,)),  
                "req_profile_seq":(v.required,(v.numeric,)),
            },
        "GET_PAYMENT_HASH":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "user_seq":(v.required,(v.numeric,)),
                "amount":(v.required,(v.numeric,)),
                "redirect":(v.required,(v.url,)),
                "subscribe_type":(v.required,(v.values,{'POST','PROFILE'})),
                "subscribe_seq":(v.required,(v.numeric,)),
            },
        "UPDATE_PAYMENT_ORDER":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "user_seq":(v.required,(v.numeric,)),
                "init_seq":(v.required,(v.numeric,)),
                "status":(v.required,(v.values,{'SUCCESS','FAILED'})),
                "order_id":(v.required,(v.alphanumericspecial,)),
                "raw_resp":(v.optional,),
                "amount":(v.required,(v.numeric,)),
            },
        "PAYU_PAYMENT":{
                
            },
        "GET_PAYMENT_STATUS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)), 
                "init_seq":(v.required,(v.numeric,)), 
            },
        "GET_PROFILE_BALANCE":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
            },
        "GET_SUBSCRIBE_DETAILS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "subscribe_profile_seq":(v.required,(v.numeric,)),
            },
        "SUBSCRIBE_PROFILE":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "user_seq":(v.required,(v.numeric,)),
                "subscribe_profile_seq":(v.required,(v.numeric,)),
            },
        "SUBSCRIBE_POST":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "user_seq":(v.required,(v.numeric,)),
                "post_seq":(v.required,(v.numeric,)),
            },
        "GET_SUBSCRIBED_PROFILES":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "type":(v.required,(v.values,{'ACTIVE','EXPIRED'})),
            },
        "GET_SUBSCRIBED_POSTS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),            
            },
        "UNSUBSCRIBE_PROFILE":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
                "subscribe_profile_seq":(v.required,(v.numeric,)),
                "subscription_seq":(v.required,(v.numeric,)),
            },
        "GET_PAYMENTS_CREDIT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
                "user_seq":(v.required,(v.numeric,)),                
            },
        "GET_PAYMENTS_DEBIT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
                "user_seq":(v.required,(v.numeric,)),                
            },
        "GET_USER_PROFILE_SUBSCRIBERS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
            },
        "GET_USER_POST_SUBSCRIBERS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),               
            },
        "GET_SUBSCRIBER_COUNT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "req_profile_seq":(v.required,(v.numeric,)),
            },
        "GET_USER_PROFILE_SUBSCRIBER_PAID":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),  
                "status":(v.required,(v.values,{'ACTIVE','ALL'})),
            },
        "REPORT_ISSUE":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "reason_code":(v.required,(v.alphanumericspecial,),(v.maxlength,16)),
                "post_seq":(v.required,(v.numeric,)),
                "comment":(v.required,(v.notallowedexq,),(v.maxlength,512)),
                "source":(v.required,(v.values,{'POST','HELP','STORY'})),
                "link_existing":(v.optional,(v.url,),(v.maxlength,256)),
                "link_stolen":(v.optional,(v.url,),(v.maxlength,256)),                
            },
        "RESTRICT_ACCOUNT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "restrict_profile_seq":(v.required,(v.numeric,)),
                "restrict_reason":(v.optional,(v.alphanumericspecial,),(v.maxlength,16)),
                "comment":(v.optional,(v.notallowedexq,),(v.maxlength,512)),                                
            },
        "BLOCK_ACCOUNT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "block_profile_seq":(v.required,(v.numeric,)),
                "block_reason":(v.optional,(v.alphanumericspecial,),(v.maxlength,16)),
                "comment":(v.optional,(v.notallowedexq,),(v.maxlength,512)),                                
            },
        "GET_BLOCKED_ACCOUNTS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                                                    
            },
        "GET_RESTRICTED_ACCOUNTS":{
               "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                               
            },
        "UNBLOCK_ACCOUNT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)), 
                "block_profile_seq":(v.required,(v.numeric,)),
                "restrict_seq":(v.required,(v.numeric,)),
            },
        "UNRESTRICT_ACCOUNT":{
               "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),   
                "restrict_profile_seq":(v.required,(v.numeric,)),
                "restrict_seq":(v.required,(v.numeric,)),
            },
        "UPLOAD_ACCOUNT_VERIFICATION":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)), 
                "type":(v.required,(v.values,{'INDIVIDUAL','GROUP','FIRM'})),
                "docu_type":(v.required,(v.values,{'AADHAR','PAN','DL','PASSPORT','VOTER'})),
                "playlist_prog":(v.required,(v.values,{'YES','NO'}))
            },
        "UPLOAD_ACCOUNT_VERIFICATION_FIRM":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)), 
                "type":(v.required,(v.values,{'INDIVIDUAL','GROUP','FIRM'})),
                "gstn":(v.required,(v.length,15),(v.allcaps,),(v.gstn,)),
                "pan":(v.required,(v.alphanumeric,),(v.maxlength,12)),
                "playlist_prog":(v.required,(v.values,{'YES','NO'}))
            },
         "DELETE_ACCOUNT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)), 
                "delete_reason":(v.required,(v.alphanumericspecial,)),
                "other_reason":(v.optional,(v.alphanumericspecial,),(v.maxlength,16)),
                "delete_comments":(v.required,(v.alphanumericspecial,),(v.maxlength,512)),
                "delete_type":(v.required,(v.values,{'DELETE','DISABLE'})),
            },
         "GET_NOTIFICATIONS_LIKES":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                                        
            },
         "GET_NOTIFICATIONS_COMMENTS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                                        
            },
         "GET_NOTIFICATIONS_TAG":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                                        
            },
         "GET_NOTIFICATIONS_SUBSCRIBES":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                                        
            },
         "GET_NOTIFICATIONS_FOLLOWS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                                        
            },
         "GET_PAYMENT_SUMMARY_VALUES":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),   
                "date_range":(v.required,(v.values,{'CURRENT_DAY_USER','PREV_DAY_USER','PAST_7_DAYS_USER','PAST_15_DAY_USER','PAST_30_DAY_USER','PAST_60_DAY_USER','PAST_90_DAY_USER','PAST_180_DAY_USR','LIFETIME_USER'})),
            },
         "GET_ALERT_SETTINGS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,))                
            },
         "UPDATE_ALERT_SETTINGS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "email_alert":(v.required,(v.values,{'YES','NO'}))
            },
         "GET_USER_MESSAGES":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),               
            },
         "UPDATE_MESSAGE_STATUS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "alert_seq":(v.required,(v.numeric,))
            },
          "CHECK_USER_HANDLE":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "user_handle":(v.required,(v.alphanumeric,),(v.maxlength,64))
            },
          "CHECK_REMOTE_DUPLICATE_USER":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "email":(v.optional,(v.email,),(v.maxlength,64)),
                "handle":(v.optional,(v.alphanumeric,),(v.maxlength,64))
            },
          "UPDATE_USER_LOCATION":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "user_seq":(v.required,(v.numeric,)),
                "country":(v.optional,(v.alphanumericspecial,),(v.maxlength,64)),
                "state":(v.optional,(v.alphanumericspecial,),(v.maxlength,64))
            },
          "GET_USER_REFERRAL_CODE":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,))                
            },
          "GET_REFERRAL_EARNED_USERS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,))
            },
          "GET_PAYOUT_DETAILS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,))
            },
          "GET_PAYOUT_HISTORY":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,))
            },
          "SUBMIT_PAYOUT_REQUEST":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "payout_months":(v.required,(v.alphanumericspecial,)),
                "gstn":(v.optional,(v.length,15),(v.allcaps,),(v.gstn,)),
            },
          "GET_POST_LIKE_USERS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "post_seq":(v.required,(v.numeric,)),                
            },
          "SEARCH_POST_LIKE_USERS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "post_seq":(v.required,(v.numeric,)),
                "search_str":(v.required,(v.alphanumericspecial,)),
            },
          "SUBMIT_USER_FEEDBACK":{  
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),
                "report_seq":(v.required,(v.numeric,)),
                "rating":(v.required,(v.numeric,)),
                "comment":(v.required,(v.alphanumericspecial,),(v.maxlength,2048)),
            },
          "DELETE_USER_POST_COMMENT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "comment_seq":(v.required,(v.numeric,)),
            },
          "UPDATE_POST_COMMENT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "post_seq":(v.required,(v.numeric,)),
                "comments":(v.optional,(v.notallowedexq,),(v.maxlength,2048)),
            },
          "SAVE_FCM_KEY":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "fcm_key":(v.required,(v.alphanumericspecial,),(v.maxlength,256)),                
            },
          "GET_USER_SEQ":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "user_handle":(v.required,(v.alphanumeric,),(v.maxlength,64))                
            },    
          "GET_TAGGED_USERS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "post_seq":(v.required,(v.numeric,)),                
            },   
          "GET_POSTS_TAGGED":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),                
            }, 
          "GET_USER_TO_TAG":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "profile_seq":(v.required,(v.numeric,)),  
                "search_str":(v.optional,(v.alphanumericspecial,)), 
            },
           "VALIDATE_TAGGING":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "tag_profile_seq":(v.required,(v.numeric,)),                 
            },
           "GET_STORY_DETAILS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "story_seq":(v.required,(v.numeric,)),                 
            },
           "UPDATE_NOTIFICATION_VIEW":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "view_tab":(v.required,(v.values,{'SUBSCRIBE','LIKE','COMMENTS','FOLLOW','TAG'})),               
            },
           "GET_NOTIFICATION_STATUS":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),                             
            },
           "GET_FINANCIAL_REPORT":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),
                "date_range":(v.required,(v.values,{'CURRENT_DAY_USER','PREV_DAY_USER','PAST_7_DAYS_USER','PAST_15_DAY_USER','PAST_30_DAY_USER','PAST_60_DAY_USER','PAST_90_DAY_USER','PAST_180_DAY_USR','LIFETIME_USER'})),
            },
           "GET_POPULAR_CATEGORIES":{
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),                             
            },
           "SEND_EMAIL":{                
                "password":(v.required,(v.values,{'hZ610Ax5$w23(thq1!3s0Kfg'})),
                "first_name":(v.required,(v.alphanumericspecial,),(v.maxlength,32)),
                "last_name":(v.optional,(v.alphanumericspecial,),(v.maxlength,32)),
                "email":(v.required,(v.email,),(v.maxlength,64)),
                "mobile":(v.optional,(v.numeric,),(v.maxlength,12)),
                "comments":(v.optional,(v.alphanumericspecial,),(v.maxlength,256)),
            },               
           "GET_PAY_ORDER_STATUS":{ 
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),    
                "order_id":(v.required,(v.alphanumericspecial,),(v.maxlength,64)),                
            },
           "UPDATE_SHARE_COUNT":{ 
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),    
                "post_seq":(v.required,(v.numeric,)),                
            },
           "UPDATE_VERIFY_NOTIFY":{ 
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),    
                "profile_seq":(v.required,(v.numeric,)),         
            },
           "GET_INTEREST_LIST":{ 
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),                    
            },
           "GET_INTEREST_CATEGORIES":{ 
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),                    
            },
           "GET_USER_INTERESTS":{ 
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),                    
            },
           "UPDATE_USER_INTERESTS":{ 
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),   
                "interests":(v.required,)
            },
           "UPDATE_USER_UI_COLOUR":{ 
                "user_id":(v.required,(v.email,),(v.maxlength,64)),
                "password":(v.required,),
                "_access_key":(v.required,(v.alphanumeric,),(v.maxlength,32)),   
                "ui_colour":(v.required,(v.alphanumericspecial,),(v.maxlength,16))
            },
           "GET_LOCATION_LIST": {
                "user_id":(v.required,(v.maxlength,256)),
                "password":(v.required,(v.maxlength,256)),                
            },
           "GET_PLAYLIST_SHOWS": {
                "user_id":(v.required,(v.maxlength,256)),
                "password":(v.required,(v.maxlength,256)),
                "req_user_seq":(v.optional,(v.numeric,)), 
            },
           "GET_PLAYLIST_EPISODES": {
                "user_id":(v.required,(v.maxlength,256)),
                "password":(v.required,(v.maxlength,256)),
                "sequence":(v.required,(v.numeric,)), 
                "type":(v.required,(v.values,{'SHOW','SEASON'})),
            },
           "GET_PLAYLIST_CLIPS": {
                "user_id":(v.required,(v.maxlength,256)),
                "password":(v.required,(v.maxlength,256)),
                "season_seq":(v.required,(v.numeric,)),
                "sel_post_seq":(v.optional,(v.numeric,)),
            },
    }