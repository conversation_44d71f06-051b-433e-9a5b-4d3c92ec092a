import os
from dotenv import load_dotenv
from pathlib import Path

load_dotenv()
class AppConfigs:

    BASE_TEMP_DIR = "/tmp" if os.getenv("ENVIRONMENT") != "TEST" else "D:/SoTrue/logs"

    log_file_path = os.getenv("LOG_FILE_PATH", os.path.join(BASE_TEMP_DIR, "sotureapp.log"))

    temp_folder_path = os.getenv("TEMP_FOLDER_PATH", BASE_TEMP_DIR)
    #
    # Audit DB Key
    #
    audit_db_key = "sotruedb"

    #
    # Audit DB Schema Key
    #
    audit_schema_key = "sotrueschema"

    #
    # Session Expiry Time in seconds
    #
    session_expiry = 900

    #
    # The time zone
    #
    time_zone = "Asia/Calcutta"

    #
    # The database connection parameters for a db key in the query files
    #
    ENVIRONMENT = os.getenv("ENVIRONMENT", "LOCAL")
    db_connection_params = {
        "sotruedb": dict(
            db_host=os.getenv(f"{ENVIRONMENT}_DB_HOST", "localhost"),
            db_user=os.getenv(f"{ENVIRONMENT}_DB_USER", "postgres"),
            db_password=os.getenv(f"{ENVIRONMENT}_DB_PASSWORD", ""),
            db_database=os.getenv(f"{ENVIRONMENT}_DB_NAME", "sotruedb"),
            db_port=int(os.getenv(f"{ENVIRONMENT}_DB_PORT", 5432))
        ),
        # Additional DB keys can be added similarly
        # "sotruedb2": dict(
        #     db_host=os.getenv("LOCAL_DB_HOST", "localhost"),
        #     db_user=os.getenv("LOCAL_DB_USER", "postgres"),
        #     db_password=os.getenv("LOCAL_DB_PASSWORD", "testpwd1"),
        #     db_database=os.getenv("LOCAL_DB_NAME", "sotruedb2"),
        #     db_port=int(os.getenv("LOCAL_DB_PORT", 5432))
        # )
    }

    #
    # The schema mappings to a schema name in the query files
    #
    db_schemas = {"sotrueschema":"sotrue","sotrueschema2":"sotrue"}

    #
    # Email configurations
    #
    # email_host = "mail.sotrue.website"
    # email_user = "<EMAIL>"
    # email_password = "3f00@4_([jWM"
    # email_sender = "<EMAIL>"
    email_host = "smtp.gmail.com"
    email_user = "<EMAIL>"
    email_password = "lwcp hrzx vdfj pnwb"
    email_sender = "<EMAIL>"

    #
    # The mapping of service groupings to url paths
    #
    service_urls = [
                    {"11":"sotrueapp/appservice"},                    
                    ]

    #
    # Messages for the generic components
    #
    messages = {
                "_E001_":"Missing Action Code",
                "_E002_":"Session Expired or Access Not Allowed",
                "_E003_":"Missing Mandatory Inputs",
                "_E004_":"Data Retrieved Successfully",
                "_E005_":"Missing Mandatory Value",
                "_E006_":"You are not authorized to perform this action"
                }    

    #
    # SMS Gateway Configuration
    #
    sms_username=""
    sms_password=""
    sms_sender_id=""    

    #
    # Razorpay Payment Gateway Configuration
    #
    razorpay_key_id="rzp_test_LCd7QUKFb5pH2V"
    razorpay_key_secret="o3ttnHDIgnA9PnuiyHXdV2ZU"
    razorpay_base64_auth='cnpwX3Rlc3RfcUZRTEc0bXpLQUw4SGE6ZjcxcUpuM0had0ZVTlVsZVZyNTVaVjR2'
    razorpay_x_acc='4564562047504256'    
    
    #
    # The app name - leave it blank
    #
    app_name = ""

    #
    # The identifier for the server
    #
    server_key="SERVER1"

    #
    # The FCM Server Key & URL
    #
    #fcm_server_key='AAAAQqkhuqY:APA91bH_QU_fwv7EfmAtI_NTBG18cH1LO7HQkqbhPfzexJllZ0j0DITTQ9GKKqwNI995D-LUkl_6jVCyb1tTGvEqYaBjzVot69vGOACKVy_zAtx22H-4xviuZfOMkhflYD2dVswfWhpc'
    #fcm_server_url = 'https://fcm.googleapis.com/fcm/send'
    fcm_key_file = str(Path(__file__).resolve().parent.parent / "sotrue-app-6800c91fe18e.json")
    fcm_server_url='https://fcm.googleapis.com/v1/projects/sotrue-app/messages:send'


    #
    # AWS S3 Settings
    #
    s3_container="sotrue-storage-1"
    s3_posts_folder="posts/"
    s3_stories_folder="stories/"
    s3_profiles_folder="profiles/"
    s3_accounts_folder="accounts/"
    s3_others_folder="others/"
    #!!!!Never set this to FALSE!!!!!
    s3_enabled = True
    s3_expiry = 900
    s3_key_id='K1Z1CHK1S6UJQ1'
    cloud_front_url='https://d3lmc323tbd9vr.cloudfront.net/'
    cloud_front_enabled = True      
    aws_access_key_id = os.getenv(f"{ENVIRONMENT}_AWS_ACCESS_KEY_ID")
    aws_secret_access_key = os.getenv(f"{ENVIRONMENT}_AWS_SECRET_ACCESS_KEY") 
    aws_region = os.getenv(f"{ENVIRONMENT}_AWS_REGION")
    #
    # Mix Panel Mode
    #
    mixpanel_mode = ""