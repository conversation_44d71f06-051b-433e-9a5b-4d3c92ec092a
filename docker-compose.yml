networks:
  sotrue-network:
    driver: bridge

services:
  web:
    build: .
    container_name: sotrue-backend
    volumes:
      - .:/app
      - C:/tmp/sotruelogs:/tmp/sotruelogs
    ports:
      - "8000:8000"
    env_file:
      - .env
    environment:
      - PORT=8000
      - ALLOWED_HOSTS=*
      - LOG_FILE_PATH=/tmp/sotruelogs/sotureapp.log
      - TEMP_FOLDER_PATH=/tmp/sotruelogs/
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 2G
        reservations:
          cpus: "1"
          memory: 1G
