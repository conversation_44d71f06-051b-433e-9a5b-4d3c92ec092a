FROM python:3.10-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PIP_ONLY_BINARY=:all:
ENV LANG=en_US.UTF-8
ENV LANGUAGE=en_US:en
ENV LC_ALL=en_US.UTF-8

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    postgresql-client \
    ffmpeg \
    python3-dev \
    python3-numpy \
    python3-pip \
    libmagic1 \
    locales \
    dnsutils \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set up locales
RUN sed -i -e 's/# en_US.UTF-8 UTF-8/en_US.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# en_IN UTF-8/en_IN UTF-8/' /etc/locale.gen && \
    locale-gen

# Install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip \
    && pip install -r requirements.txt

# Copy project
COPY . .

RUN echo "Applying database migrations..." \
    && sleep 10 \
    && cd sotruebackend \
    && python manage.py migrate \
    && cd ..

RUN echo "Setting database host..." && sed -i "s/'HOST': 'localhost'/'HOST': 'localhost'/g" sotruebackend/sotruebackend/settings.py

CMD ["sh", "-c", "cd sotruebackend && gunicorn sotruebackend.wsgi:application --bind 0.0.0.0:8000 --timeout 300 --workers 2 --threads 4"]
