# Exported from Render on 2025-05-11T13:37:13Z
services:
  - type: web
    name: sotrue-backend
    runtime: python
    repo: https://github.com/varenyamnikam/sotrue-backend
    plan: free
    region: singapore
    rootDir: sotruebackend

    build:
      pythonVersion: 3.10 # 👈 Add this

    envVars:
      - key: PIP_ONLY_BINARY
        value: ":all:"
      - key: ENVIRONMENT
        value: TEST
      - key: TEST_DB_HOST
        value: dpg-d0fitga4d50c73eumb20-a.singapore-postgres.render.com
      - key: TEST_DB_PORT
        value: "5432"
      - key: TEST_DB_NAME
        value: sotruedb_yf96
      - key: TEST_DB_USER
        value: varenyam
      - key: TEST_DB_PASSWORD
        value: Fi6KRzYV24oexyjwpounjDLkmzPelBbr
      - key: ALLOWED_HOSTS
        value: "*"
      - key: LOG_FILE_PATH
        value: /tmp/sotruelogs/sotureapp.log
      - key: TEMP_FOLDER_PATH
        value: /tmp/sotruelogs/
      - key: SESSION_EXPIRY
        value: "900"
      - key: TIME_ZONE
        value: Asia/Calcutta

    buildCommand: |
    pip install --upgrade pip
    pip install --only-binary=:all: -r requirements.txt
    echo "✅ Build complete"

    startCommand: gunicorn sotruebackend.wsgi:application --bind 0.0.0.0:$PORT

version: "1"
